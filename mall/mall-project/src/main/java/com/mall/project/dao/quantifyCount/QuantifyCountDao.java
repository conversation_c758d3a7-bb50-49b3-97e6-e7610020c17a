package com.mall.project.dao.quantifyCount;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;

/**
 * 量化数计算数据访问对象
 */
@Repository
@Slf4j
public class QuantifyCountDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 量化数设置查询
     */
    public Map<String,Object> getQuantifyCount(){
        // 对C用户量化数进行计算
        String sql = "SELECT is_enabled,proportion FROM quantify_set WHERE id = 1";
        try {
            return jdbcTemplate.queryForMap(sql);
        } catch (Exception e) {
            // 当数据库中没有数据时，返回空的Map，让Service层处理
            return new HashMap<>();
        }
    }
    /**
     * 每天的量化数计算
     */
    public void updateQuantifyCount() {
        // 对C用户量化数进行计算
        String sql = "SELECT is_enabled,proportion FROM quantify_set WHERE id = 1";
        try {
            Map<String, Object> dataMap = null;
            try {
                dataMap = jdbcTemplate.queryForMap(sql);
            } catch (EmptyResultDataAccessException e) {
                log.warn("quantify_set表中没有数据，跳过C用户量化数计算");
                dataMap = new HashMap<>();
            }

            if(dataMap != null && !dataMap.isEmpty() && dataMap.get("is_enabled") != null && dataMap.get("is_enabled").toString().equals("0")){
                // 对C用户量化数进行计算
                String proportion = dataMap.get("proportion").toString();
                sql = "UPDATE mall_b_users_count c\n" +
                        "JOIN mall_b_users u ON c.phone = u.phone\n" +
                        "SET c.quantify_count = CAST(c.weight_count AS DECIMAL(20,2)) * ?\n" +
                        "WHERE u.user_type = 'C'\n" +
                        "  AND c.update_time = CURDATE()";
                jdbcTemplate.update(sql, new BigDecimal(proportion).divide(new BigDecimal(100)));
            }

            // 对B用户量化数进行计算
            sql = "SELECT on_off FROM area_authorize_set WHERE enterprise_id = 1";   // 判断区域授权是否开启
            Map<String, Object> onOffMap = null;
            try {
                onOffMap = jdbcTemplate.queryForMap(sql);
            } catch (EmptyResultDataAccessException e) {
                log.warn("area_authorize_set表中没有数据，跳过B用户量化数计算");
                return;
            }

            sql = "SELECT on_off FROM area_proportion";                              // 判断区域授权比例是否开启
            Map<String, Object> areaProportionMap = null;
            try {
                areaProportionMap = jdbcTemplate.queryForMap(sql);
            } catch (EmptyResultDataAccessException e) {
                log.warn("area_proportion表中没有数据，跳过B用户量化数计算");
                return;
            }
            //只有区域授权和区域授权比例都开启时，才进行量化数计算
            if(onOffMap != null && onOffMap.get("on_off") != null &&
               areaProportionMap != null && areaProportionMap.get("on_off") != null &&
               onOffMap.get("on_off").toString().equals("0") &&
               areaProportionMap.get("on_off").toString().equals("0")){

                sql = "SELECT phone,area_id,start_time,end_time FROM area_authorize WHERE type = 'B'";   //只对B用户进行量化数计算
                List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql);
                for (Map<String, Object> data : dataList) {
                    String phone = data.get("phone").toString();
                    String areaId = data.get("area_id").toString();
                    String startTime = data.get("start_time").toString();
                    String endTime = data.get("end_time").toString();
                    //判断startTime 和 endTime 是否在当前时间范围内
                    boolean isInRange = jdbcTemplate.queryForObject("SELECT CASE WHEN NOW() BETWEEN ? AND ? THEN 1 ELSE 0 END AS is_in_range", Integer.class, startTime, endTime) == 1;
                    if(isInRange){
                        sql = "select u.phone from mall_b_users u where u.user_type = 'B' and town_code in(\n" +
                                "WITH RECURSIVE area_cte AS (\n" +
                                "    SELECT id\n" +
                                "    FROM area\n" +
                                "    WHERE parent_id = ? \n" +
                                "    UNION ALL\n" +
                                "    SELECT a.id\n" +
                                "    FROM area a\n" +
                                "    INNER JOIN area_cte c ON a.parent_id = c.id\n" +
                                ")\n" +
                                "SELECT * FROM area_cte)";
                        List<Map<String, Object>> phoneList= jdbcTemplate.queryForList(sql, areaId);
                        StringBuilder phoneBuilder = new StringBuilder(1024 * 1024); // 预分配1MB缓冲区
                        phoneBuilder.append(phone);
                        boolean first = false;
                        for (Map<String, Object> row : phoneList) {
                            // 从每行数据中获取 phone 值
                            String phones = String.valueOf(row.get("phone"));
                            if (!first) {
                                phoneBuilder.append(","); // 从第二个号码开始添加逗号
                            } else {
                                first = false;
                            }
                            phoneBuilder.append(phones);
                        }
                        //System.out.println("======================" + removeDuplicates(phoneBuilder.toString()));
                        String totalCountSql = "SELECT COALESCE(sum(total_count), 0) as total_count FROM enterprise_product_data where phone in("+removeDuplicates(phoneBuilder.toString())+") and enterprise_id = 1 and DATE(update_time) = CURDATE() ";

                        String totalCount = "0";
                        try {
                            Map<String, Object> totalCountMap = jdbcTemplate.queryForMap(totalCountSql);
                            Object totalCountObj = totalCountMap.get("total_count");
                            totalCount = (totalCountObj != null) ? totalCountObj.toString() : "0";
                        } catch (EmptyResultDataAccessException e) {
                            log.warn("enterprise_product_data表中没有数据，使用默认值0");
                            totalCount = "0";
                        }

                        String areaLevelSql = "select level from area_authorize z, area a where z.area_id = a.id and z.area_id = ?";
                        Map<String, Object> levelMap = null;
                        try {
                            levelMap = jdbcTemplate.queryForMap(areaLevelSql, areaId);
                        } catch (EmptyResultDataAccessException e) {
                            log.warn("area_authorize或area表中没有数据，跳过该区域的量化数计算");
                            continue;
                        }
                        if(levelMap != null && levelMap.get("level") != null) {
                            String levelSql = "";
                            String level = levelMap.get("level").toString();
                            if("1".equals(level)){
                                levelSql = "SELECT lvel1_proportion as proportion FROM area_proportion";
                            }else if("2".equals(level)){
                                levelSql = "SELECT lvel2_proportion as proportion FROM area_proportion";
                            }else if("3".equals(level)){
                                levelSql = "SELECT lvel3_proportion as proportion FROM area_proportion";
                            }else if("4".equals(level)){
                                levelSql = "SELECT lvel4_proportion as proportion FROM area_proportion";
                            }

                            if(!levelSql.isEmpty()) {
                                try {
                                    Map<String, Object> proportionMap = jdbcTemplate.queryForMap(levelSql);
                                    if(proportionMap != null && proportionMap.get("proportion") != null) {
                                        BigDecimal proportion = new BigDecimal(proportionMap.get("proportion").toString());
                                        BigDecimal quantifyCount = new BigDecimal(totalCount).multiply(proportion.divide(new BigDecimal(100)));
                                        sql = "UPDATE mall_b_users_count SET quantify = ?,quantify_count = ? WHERE phone = ? and update_time = CURDATE() ";
                                        jdbcTemplate.update(sql, quantifyCount,quantifyCount.add(new BigDecimal(lastDayQuantifyCount(phone))), phone);
                                    }
                                } catch (EmptyResultDataAccessException e) {
                                    log.warn("area_proportion表中没有对应级别的比例数据，跳过该区域的量化数计算");
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("量化数计算失败: {}", e.getMessage(), e);
        }
    }
    /**
     *  获取历史到昨日的总量化数
     */
    public String lastDayQuantifyCount(String phone) {
        try{
            String sql = "SELECT quantify_count FROM mall_b_users_count WHERE phone = ? and update_time = CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }
    //去除逗号分隔字符串中的重复项
    public static String removeDuplicates(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 分割字符串
        String[] parts = input.split(",");

        // 使用 LinkedHashSet 保持顺序并去重
        LinkedHashSet<String> uniqueSet = new LinkedHashSet<>();
        for (String part : parts) {
            uniqueSet.add(part.trim()); // 去除空格
        }

        // 重新拼接为字符串
        return String.join(",", uniqueSet);
    }
    /**
     * 量化数设置
     */
    public int saveOrUpdateQuantifyCount(String isEnabled,String proportion,Integer updatePerson) {
        // quantify_set 表 没有数据没有数据则插入数据,有数据再更新数据 使用 ON DUPLICATE KEY UPDATE  字段有： id,is_enabled,proportion,update_person,update_time
        String sql = "INSERT INTO quantify_set(id, is_enabled, proportion, update_person, update_time) VALUES (1, ?, ?, ?, NOW()) " +
                "ON DUPLICATE KEY UPDATE " +
                "is_enabled = VALUES(is_enabled), proportion = VALUES(proportion), update_person = VALUES(update_person), update_time = NOW()";
       return jdbcTemplate.update(sql,isEnabled,proportion,updatePerson);
    }

    /**
     * 量化数查询
     */
    public List<Map<String,Object>> quantifyCount(String phone,String startTime,String endTime,int limit, int offset){
        // 参数
        List<Object> params = new ArrayList<>();
        String sql = "select DATE(c.update_time) as update_time,c.phone,u.username,c.quantify,c.Weight,c.weight_count,c.quantify_count from mall_b_users_count c,mall_b_users u\n" +
                "WHERE c.phone = u.phone";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND c.phone like ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        sql += " ORDER BY c.update_time DESC,c.id DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     *  量化数设置 导出 Excel
     */
    public List<Map<String,Object>> exportQuantifyCountExcel(String startTime,String endTime){
        // 参数
        List<Object> params = new ArrayList<>();
        String sql = "select DATE(c.update_time) as update_time,c.phone,u.username,c.quantify,c.Weight,c.weight_count,c.quantify_count from mall_b_users_count c,mall_b_users u\n" +
                "WHERE c.phone = u.phone";

        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        sql += " ORDER BY c.update_time DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * 总量化数条数
     */
    public int totalQuantifyCount(String phone,String startTime,String endTime){
        // 参数
        List<Object> params = new ArrayList<>();
        String sql = "select count(1) from mall_b_users_count c,mall_b_users u WHERE c.phone = u.phone";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND c.phone like ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    //计算 今日总量化数
    public String todaytotalQuantify(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        try{
            String sql = "select COALESCE(sum(quantify), '0') as todaytotalQuantify from mall_b_users_count WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_time) = CURDATE() ";
            }
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        }catch (Exception e){
            return "0";
        }
    }

    // 计算 累计量化数
    public String weightCountTotal(String phone, String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(COALESCE(quantify_count, 0)) AS total_quantify_count\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        quantify_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY phone \n" +
                "            ORDER BY update_time DESC, id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count Where 1 = 1";
        try {
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_time) = CURDATE() ";
            }
            sql += " ) AS ranked WHERE rn = 1";
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询累计量化数失败: {}", e.getMessage());
            return "0";
        }
    }
    // 计算 中南惠C的每日所有ID累计量化数
    public String cweightCountTotal(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(sum(quantify), 0) as today_cquantify_count\n" +
                "FROM mall_b_users_count c, mall_b_users u\n" +
                "WHERE c.phone = u.phone\n" +
                "and u.user_type = 'C'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) = CURDATE() - INTERVAL 1 DAY";
        }
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询C用户每日量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    // 计算 中南惠所有B的每日累计量化数
    public String bweightCountTotal(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(sum(quantify), 0) as today_bquantify_count\n" +
                "FROM mall_b_users_count c, mall_b_users u\n" +
                "WHERE c.phone = u.phone\n" +
                "and u.user_type = 'B'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) = CURDATE() ";
        }
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询B用户每日量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    // 查看 所有合作企业Admain的各ID每日每笔数据量化数比 得出 Admin的每日累计量化数

    public String getAdminDailyQuantity(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT is_enabled, daily_data_percentage FROM partner_enterprise_admin_data WHERE id = 1";
        String adminDailyQuantity = "0";
        Map<String, Object> dataMap = jdbcTemplate.queryForMap(sql);
        if("0".equals(dataMap.get("is_enabled").toString()) && !"".equals(dataMap.get("daily_data_percentage"))){
            // 今日总合计数
            sql = "SELECT COALESCE(SUM(today_total_count),0) AS today_quantity FROM enterprise_trade_stats WHERE 1 = 1";
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_time) = CURDATE() ";
            }
            String todayQuantity = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            if(!"0.00".equals(todayQuantity)){
                BigDecimal percentage = new BigDecimal(String.valueOf(dataMap.get("daily_data_percentage")));            // 所有合作企业Admain的各ID每日每笔数据量化数比
                BigDecimal result = new BigDecimal(todayQuantity).multiply(percentage).divide(new BigDecimal(100));  // 今日总合计数 乘以 所有合作企业Admain的各ID每日每笔数据量化数比
                adminDailyQuantity = result.toString();
            }
        }
        // 把adminDailyQuantity 插入到 enterprise_trade_stats 表中,如果当天的数据不存在,则插入,如果存在,则更新
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM enterprise_trade_stats WHERE enterprise_id = 1 AND DATE(update_time) = CURDATE() )", Integer.class) == 0) {
            sql = "INSERT INTO enterprise_trade_stats(enterprise_id,admin_daily_quantity,update_time)VALUES(1, ?, CURDATE() )";
            jdbcTemplate.update(sql, adminDailyQuantity);
        }else{
            sql = "UPDATE enterprise_trade_stats SET admin_daily_quantity = ? WHERE enterprise_id = 1 AND DATE(update_time) = CURDATE() ";
            jdbcTemplate.update(sql, adminDailyQuantity);
        }
        String yesterdayQuantitySql = "select admin_daily_quantity from enterprise_trade_stats WHERE 1 = 1";
        List<Object> yesTerdayParams = new ArrayList<>();
        if (startTime != null && !startTime.isEmpty()) {
            yesterdayQuantitySql += " AND DATE(update_time) = ?";
            yesTerdayParams.add(startTime);
        }else{
            yesterdayQuantitySql += " AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
        }
        try {
            if(yesTerdayParams.isEmpty()){
                return jdbcTemplate.queryForObject(yesterdayQuantitySql, String.class);
            }else{
                return jdbcTemplate.queryForObject(yesterdayQuantitySql, String.class, yesTerdayParams.toArray());
            }
        } catch (EmptyResultDataAccessException e) {
            return "0";
        }
    }

    /**
     * 计算 每日Admin量化值,使用 今日总合计数 乘以当日量化率
     */
    public String adminDailyQuantifyValue(String startTime){
        List<Object> params = new ArrayList<>();
        // 如果当天的数据不存在,则返回0
        String sql = "SELECT COALESCE(SUM(today_total_count), 0) AS today_quantity FROM enterprise_trade_stats WHERE 1=1";
        try {
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_time) = ?";
                params.add(startTime);
            }else{
                sql += " AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * Admin的累计量化数
     */
    public String adminTotalQuantity(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "select COALESCE(sum(admin_daily_quantity), '0') as admin_daily_quantity from enterprise_trade_stats where enterprise_id = 1";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(update_time) <= ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(update_time) <= CURDATE() - INTERVAL 1 DAY ";
        }
        try {
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 计算 中南惠C的所有ID累计量化数
     */
    public String cweightCountTotalAllDays(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(ranked.quantify_count) AS total_quantify_count\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        c.quantify_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY c.phone \n" +
                "            ORDER BY c.update_time DESC, c.id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count c,mall_b_users u\n" +
                "    WHERE c.phone = u.phone\n" +
                "    and u.user_type = 'C'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) <= ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询C用户所有ID累计量化数失败: {}", e.getMessage());
            return "0";
        }
    }

    /**
     * 计算 中南惠所有B的累计量化数
     */
    public String bweightCountTotalAlldays(String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT SUM(ranked.quantify_count) AS total_quantify_count\n" +
                "FROM (\n" +
                "    SELECT \n" +
                "        c.quantify_count,\n" +
                "        ROW_NUMBER() OVER (\n" +
                "            PARTITION BY c.phone \n" +
                "            ORDER BY c.update_time DESC, c.id DESC\n" +
                "        ) AS rn\n" +
                "    FROM mall_b_users_count c,mall_b_users u\n" +
                "    WHERE c.phone = u.phone\n" +
                "    and u.user_type = 'B'";
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(c.update_time) <= ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(c.update_time) <= CURDATE() ";
        }
        sql += " ) AS ranked WHERE rn = 1";
        try {
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            log.warn("查询B用户所有ID累计量化数失败: {}", e.getMessage());
            return "0";
        }
    }
}
